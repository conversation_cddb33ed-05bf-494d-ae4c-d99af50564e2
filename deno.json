{"name": "mattermost-chatbot", "version": "1.0.0", "description": "A Mattermost chatbot server built with Deno", "exports": "./src/main.ts", "tasks": {"dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch src/main.ts", "start": "deno run --allow-net --allow-read --allow-env --allow-write src/main.ts", "test": "deno test --allow-net --allow-read --allow-env --allow-write", "test:watch": "deno test --allow-net --allow-read --allow-env --allow-write --watch", "fmt": "deno fmt", "fmt:check": "deno fmt --check", "lint": "deno lint", "check": "deno check src/main.ts", "compile": "deno compile --allow-net --allow-read --allow-env --allow-write --output ./dist/chatbot src/main.ts", "bundle": "deno bundle src/main.ts dist/bundle.js"}, "imports": {"@std/path": "jsr:@std/path@^1.0.0", "@std/fs": "jsr:@std/fs@^1.0.0", "@std/log": "jsr:@std/log@^0.224.0", "@std/dotenv": "jsr:@std/dotenv@^0.225.0", "@std/assert": "jsr:@std/assert@^1.0.0", "@std/testing": "jsr:@std/testing@^1.0.0", "@std/cli": "jsr:@std/cli@^1.0.0"}, "compilerOptions": {"lib": ["deno.ns", "deno.window"], "strict": true}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["src/", "tests/"], "exclude": ["dist/", "node_modules/"]}, "lint": {"include": ["src/", "tests/"], "exclude": ["dist/", "node_modules/"], "rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}, "test": {"include": ["tests/", "src/**/*_test.ts", "src/**/*.test.ts"], "exclude": ["dist/", "node_modules/"]}}