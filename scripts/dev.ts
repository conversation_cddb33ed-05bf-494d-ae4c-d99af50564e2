#!/usr/bin/env -S deno run --allow-run --allow-read --allow-env

/**
 * Development script with additional features
 */

import { parseArgs } from "@std/cli/parse-args";

const args = parseArgs(Deno.args, {
  boolean: ["help", "check", "fmt", "lint"],
  string: ["port"],
  alias: {
    h: "help",
    p: "port",
    c: "check",
    f: "fmt",
    l: "lint",
  },
});

if (args.help) {
  console.log(`
Development script for Mattermost Chatbot

Usage: deno run --allow-run --allow-read --allow-env scripts/dev.ts [options]

Options:
  -h, --help     Show this help message
  -p, --port     Set the port number (default: 3000)
  -c, --check    Run type checking before starting
  -f, --fmt      Format code before starting
  -l, --lint     Lint code before starting

Examples:
  deno run --allow-run --allow-read --allow-env scripts/dev.ts
  deno run --allow-run --allow-read --allow-env scripts/dev.ts --port 8080
  deno run --allow-run --allow-read --allow-env scripts/dev.ts --check --fmt --lint
`);
  Deno.exit(0);
}

async function runCommand(cmd: string[]): Promise<boolean> {
  console.log(`Running: ${cmd.join(" ")}`);
  const process = new Deno.Command(cmd[0], {
    args: cmd.slice(1),
    stdout: "inherit",
    stderr: "inherit",
  });

  const { success } = await process.output();
  return success;
}

async function main() {
  console.log("🚀 Starting development server...\n");

  // Format code if requested
  if (args.fmt) {
    console.log("📝 Formatting code...");
    const success = await runCommand(["deno", "fmt"]);
    if (!success) {
      console.error("❌ Formatting failed");
      Deno.exit(1);
    }
    console.log("✅ Code formatted\n");
  }

  // Lint code if requested
  if (args.lint) {
    console.log("🔍 Linting code...");
    const success = await runCommand(["deno", "lint"]);
    if (!success) {
      console.error("❌ Linting failed");
      Deno.exit(1);
    }
    console.log("✅ Code linted\n");
  }

  // Type check if requested
  if (args.check) {
    console.log("🔧 Type checking...");
    const success = await runCommand(["deno", "check", "src/main.ts"]);
    if (!success) {
      console.error("❌ Type checking failed");
      Deno.exit(1);
    }
    console.log("✅ Type checking passed\n");
  }

  // Set port if provided
  if (args.port) {
    Deno.env.set("PORT", args.port);
    console.log(`🔧 Port set to ${args.port}`);
  }

  // Start the development server
  console.log("🎯 Starting server with watch mode...\n");
  const devCmd = [
    "deno",
    "run",
    "--allow-net",
    "--allow-read",
    "--allow-env",
    "--watch",
    "src/main.ts",
  ];

  await runCommand(devCmd);
}

if (import.meta.main) {
  await main();
}
