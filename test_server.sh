#!/bin/bash

echo "🚀 Testing Mattermost Chatbot Server"
echo "======================================"

# Add Deno to PATH for this session
export PATH="/Users/<USER>/.deno/bin:$PATH"

# Start the server in the background
echo "Starting server..."
deno run --allow-net --allow-read --allow-env src/main.ts &
SERVER_PID=$!

# Wait a moment for server to start
sleep 3

echo "Testing endpoints..."

# Test health endpoint
echo "1. Testing /health endpoint:"
curl -s http://localhost:3000/health | jq '.' || echo "Health check failed"

echo -e "\n2. Testing root endpoint:"
curl -s http://localhost:3000/ | jq '.' || echo "Root endpoint failed"

echo -e "\n3. Testing webhook endpoint (should require POST):"
curl -s http://localhost:3000/webhook | jq '.' || echo "Webhook GET test completed"

echo -e "\n4. Testing webhook with POST:"
curl -s -X POST -H "Content-Type: application/json" \
  -d '{"text":"!bot ping","user_name":"testuser","channel_name":"general","trigger_word":"!bot","token":"test","team_id":"team1","team_domain":"test","channel_id":"ch1","timestamp":**********,"user_id":"user1","post_id":"post1","file_ids":""}' \
  http://localhost:3000/webhook | jq '.' || echo "Webhook POST test completed"

# Clean up
echo -e "\nStopping server..."
kill $SERVER_PID 2>/dev/null

echo "✅ Server tests completed!"
