# Mattermost Chatbot

A modern, scalable chatbot server for Mattermost built with Deno and TypeScript.

## Features

- 🚀 Built with Deno for modern JavaScript/TypeScript runtime
- 🔒 Secure webhook handling with optional authentication
- 📝 Comprehensive logging with configurable levels
- 🧪 Full test coverage with unit and integration tests
- 🔧 Development tools with hot reload, formatting, and linting
- 📊 Health and readiness endpoints for monitoring
- 🌐 CORS support for web integrations
- 📁 Well-organized project structure following best practices

## Project Structure

```
mattermost-chatbot/
├── src/
│   ├── config/          # Configuration management
│   ├── handlers/        # Request handlers
│   ├── middleware/      # HTTP middleware
│   ├── services/        # Business logic services
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   ├── router.ts        # Main request router
│   └── main.ts          # Application entry point
├── tests/
│   ├── unit/            # Unit tests
│   └── integration/     # Integration tests
├── scripts/             # Development and build scripts
├── docs/                # Additional documentation
├── logs/                # Application logs (created at runtime)
└── deno.json           # Deno configuration
```

## Prerequisites

- [Deno](https://deno.land/) v1.40+ installed
- Mattermost server (local or remote)
- Bot account configured in Mattermost

## Quick Start

1. **Clone and setup the project:**
   ```bash
   git clone <your-repo-url>
   cd mattermost-chatbot
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development server:**
   ```bash
   deno task dev
   ```

4. **Or use the enhanced development script:**
   ```bash
   deno run --allow-run --allow-read --allow-env scripts/dev.ts --check --fmt --lint
   ```

## Configuration

Create a `.env` file based on `.env.example`:

```env
# Server Configuration
PORT=3000
LOG_LEVEL=INFO

# Mattermost Configuration
MATTERMOST_URL=http://localhost:8065
BOT_TOKEN=your_bot_token_here

# Optional: Webhook Secret for additional security
WEBHOOK_SECRET=your_webhook_secret_here
```

### Configuration Options

- `PORT`: Server port (default: 3000)
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARN, ERROR)
- `MATTERMOST_URL`: Your Mattermost server URL
- `BOT_TOKEN`: Bot access token from Mattermost
- `WEBHOOK_SECRET`: Optional webhook authentication secret

## Available Commands

Use `deno task <command>` to run these commands:

- `dev` - Start development server with hot reload
- `start` - Start production server
- `test` - Run all tests
- `test:watch` - Run tests in watch mode
- `fmt` - Format code
- `fmt:check` - Check code formatting
- `lint` - Lint code
- `check` - Type check the code
- `compile` - Compile to executable
- `bundle` - Bundle for distribution

## API Endpoints

### Health & Status
- `GET /` - Server information
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint

### Webhook
- `POST /webhook` - Mattermost webhook handler

## Bot Commands

The bot supports these commands (triggered by your configured trigger word):

- `help` - Show available commands
- `ping` - Check if bot is responding
- `echo <message>` - Echo back your message
- `time` - Get current server time

## Development

### Running Tests

```bash
# Run all tests
deno task test

# Run tests in watch mode
deno task test:watch

# Run specific test file
deno test tests/unit/webhook_test.ts
```

### Code Quality

```bash
# Format code
deno task fmt

# Check formatting
deno task fmt:check

# Lint code
deno task lint

# Type check
deno task check
```

### Development Server

The development server includes:
- Hot reload on file changes
- Automatic restart on crashes
- Enhanced logging
- Type checking

```bash
# Basic development server
deno task dev

# Enhanced development with pre-checks
deno run --allow-run --allow-read --allow-env scripts/dev.ts --check --fmt --lint --port 8080
```

## Deployment

### Compile to Executable

```bash
deno task compile
# Creates ./dist/chatbot executable
```

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM denoland/deno:1.40.0

WORKDIR /app
COPY . .
RUN deno cache src/main.ts

EXPOSE 3000
CMD ["deno", "run", "--allow-net", "--allow-read", "--allow-env", "src/main.ts"]
```

### Environment Variables for Production

Ensure these environment variables are set in production:
- `BOT_TOKEN` (required)
- `MATTERMOST_URL` (required)
- `PORT` (optional, defaults to 3000)
- `LOG_LEVEL` (optional, defaults to INFO)
- `WEBHOOK_SECRET` (recommended for security)

## Mattermost Setup

1. **Create a Bot Account:**
   - Go to System Console → Integrations → Bot Accounts
   - Create a new bot account
   - Copy the access token

2. **Configure Outgoing Webhook:**
   - Go to your team settings → Integrations → Outgoing Webhooks
   - Create a new webhook
   - Set callback URL to: `http://your-server:3000/webhook`
   - Set trigger words (e.g., `!bot`)

3. **Test the Integration:**
   - In a Mattermost channel, type: `!bot ping`
   - The bot should respond with "Pong! 🏓 Bot is alive and responding."

## Monitoring

The server provides monitoring endpoints:

- `/health` - Basic health check
- `/ready` - Readiness check with dependency status
- Structured logging to console and files
- Request/response logging with timing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `deno task test`
5. Format code: `deno task fmt`
6. Lint code: `deno task lint`
7. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
1. Check the logs in the `logs/` directory
2. Verify your configuration in `.env`
3. Test the health endpoints
4. Review the Mattermost webhook configuration
