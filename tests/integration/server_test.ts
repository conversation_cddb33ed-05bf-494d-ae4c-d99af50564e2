import { assertEquals, assertExists } from "@std/assert";

const BASE_URL = "http://localhost:3000";

Deno.test("Server Integration Tests", async (t) => {
  await t.step("should respond to health check", async () => {
    const response = await fetch(`${BASE_URL}/health`);
    assertEquals(response.status, 200);

    const data = await response.json();
    assertEquals(data.success, true);
    assertExists(data.data.status);
    assertEquals(data.data.status, "healthy");
  });

  await t.step("should respond to ready check", async () => {
    const response = await fetch(`${BASE_URL}/ready`);
    assertEquals(response.status, 200);

    const data = await response.json();
    assertEquals(data.success, true);
    assertEquals(data.data.ready, true);
  });

  await t.step("should respond to root endpoint", async () => {
    const response = await fetch(`${BASE_URL}/`);
    assertEquals(response.status, 200);

    const data = await response.json();
    assertExists(data.name);
    assertExists(data.version);
    assertExists(data.endpoints);
  });

  await t.step("should handle 404 for unknown endpoints", async () => {
    const response = await fetch(`${BASE_URL}/unknown`);
    assertEquals(response.status, 404);

    const data = await response.json();
    assertEquals(data.error, "Not found");
  });

  await t.step("should handle CORS preflight requests", async () => {
    const response = await fetch(`${BASE_URL}/webhook`, {
      method: "OPTIONS",
    });
    assertEquals(response.status, 200);
    assertExists(response.headers.get("Access-Control-Allow-Origin"));
    assertExists(response.headers.get("Access-Control-Allow-Methods"));
  });
});
