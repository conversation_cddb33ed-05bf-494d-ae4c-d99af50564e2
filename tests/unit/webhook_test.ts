import { assertEquals, assertExists } from "@std/assert";
import { handleWebhook } from "../../src/handlers/webhook.ts";
import type { WebhookPayload } from "../../src/types/mod.ts";

Deno.test("Webhook Handler Tests", async (t) => {
  await t.step("should handle valid webhook payload", async () => {
    const payload: WebhookPayload = {
      token: "test-token",
      team_id: "team123",
      team_domain: "test-team",
      channel_id: "channel123",
      channel_name: "general",
      timestamp: Date.now(),
      user_id: "user123",
      user_name: "testuser",
      post_id: "post123",
      text: "!bot help",
      trigger_word: "!bot",
      file_ids: "",
    };

    const req = new Request("http://localhost:3000/webhook", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });

    const response = await handleWebhook(req);
    assertEquals(response.status, 200);

    const responseData = await response.json();
    assertExists(responseData.text);
  });

  await t.step("should handle ping command", async () => {
    const payload: WebhookPayload = {
      token: "test-token",
      team_id: "team123",
      team_domain: "test-team",
      channel_id: "channel123",
      channel_name: "general",
      timestamp: Date.now(),
      user_id: "user123",
      user_name: "testuser",
      post_id: "post123",
      text: "!bot ping",
      trigger_word: "!bot",
      file_ids: "",
    };

    const req = new Request("http://localhost:3000/webhook", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });

    const response = await handleWebhook(req);
    assertEquals(response.status, 200);

    const responseData = await response.json();
    assertEquals(responseData.text, "Pong! 🏓 Bot is alive and responding.");
  });

  await t.step("should handle invalid content type", async () => {
    const req = new Request("http://localhost:3000/webhook", {
      method: "POST",
      headers: { "Content-Type": "text/plain" },
      body: "invalid body",
    });

    const response = await handleWebhook(req);
    assertEquals(response.status, 400);

    const responseData = await response.json();
    assertEquals(responseData.error, "Content-Type must be application/json");
  });

  await t.step("should handle malformed JSON", async () => {
    const req = new Request("http://localhost:3000/webhook", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: "invalid json",
    });

    const response = await handleWebhook(req);
    assertEquals(response.status, 500);

    const responseData = await response.json();
    assertEquals(responseData.error, "Internal server error");
  });
});
