// Common types for the Mattermost chatbot

export interface Config {
  port: number;
  mattermostUrl: string;
  botToken: string;
  webhookSecret?: string;
  logLevel: "DEBUG" | "INFO" | "WARN" | "ERROR";
}

export interface MattermostUser {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  nickname: string;
  locale: string;
  timezone: {
    useAutomaticTimezone: boolean;
    automaticTimezone: string;
    manualTimezone: string;
  };
}

export interface MattermostChannel {
  id: string;
  create_at: number;
  update_at: number;
  delete_at: number;
  team_id: string;
  type: "O" | "P" | "D" | "G"; // Open, Private, Direct, Group
  display_name: string;
  name: string;
  header: string;
  purpose: string;
  last_post_at: number;
  total_msg_count: number;
  extra_update_at: number;
  creator_id: string;
}

export interface MattermostPost {
  id: string;
  create_at: number;
  update_at: number;
  edit_at: number;
  delete_at: number;
  is_pinned: boolean;
  user_id: string;
  channel_id: string;
  root_id: string;
  parent_id: string;
  original_id: string;
  message: string;
  type: string;
  props: Record<string, unknown>;
  hashtags: string;
  pending_post_id: string;
  reply_count: number;
  metadata: {
    embeds?: unknown[];
    emojis?: unknown[];
    files?: unknown[];
    images?: unknown[];
    reactions?: unknown[];
  };
}

export interface WebhookPayload {
  token: string;
  team_id: string;
  team_domain: string;
  channel_id: string;
  channel_name: string;
  timestamp: number;
  user_id: string;
  user_name: string;
  post_id: string;
  text: string;
  trigger_word: string;
  file_ids: string;
}

export interface BotResponse {
  text: string;
  username?: string;
  icon_url?: string;
  channel?: string;
  response_type?: "in_channel" | "ephemeral";
  attachments?: Attachment[];
}

export interface Attachment {
  fallback: string;
  color?: string;
  pretext?: string;
  author_name?: string;
  author_link?: string;
  author_icon?: string;
  title?: string;
  title_link?: string;
  text?: string;
  fields?: AttachmentField[];
  image_url?: string;
  thumb_url?: string;
  footer?: string;
  footer_icon?: string;
  ts?: number;
}

export interface AttachmentField {
  title: string;
  value: string;
  short?: boolean;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
