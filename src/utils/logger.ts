import * as log from "@std/log";
import { config } from "../config/mod.ts";

// Configure logger based on environment
const logLevel = config.logLevel;

// Ensure logs directory exists
try {
  await Deno.mkdir("logs", { recursive: true });
} catch (error) {
  if (!(error instanceof Deno.errors.AlreadyExists)) {
    console.warn("Could not create logs directory:", error);
  }
}

log.setup({
  handlers: {
    console: new log.ConsoleHandler(logLevel, {
      formatter: (logRecord) => {
        const timestamp = new Date().toISOString();
        const level = logRecord.levelName.padEnd(5);
        return `[${timestamp}] ${level} ${logRecord.msg}`;
      },
    }),
  },
  loggers: {
    default: {
      level: logLevel,
      handlers: ["console"],
    },
  },
});

export const logger = log.getLogger();

// Utility functions for structured logging
export function logInfo(message: string, data?: Record<string, unknown>): void {
  const logMessage = data ? `${message} ${JSON.stringify(data)}` : message;
  logger.info(logMessage);
}

export function logError(
  message: string,
  error?: Error,
  data?: Record<string, unknown>,
): void {
  const errorInfo = error ? ` Error: ${error.message}` : "";
  const dataInfo = data ? ` Data: ${JSON.stringify(data)}` : "";
  logger.error(`${message}${errorInfo}${dataInfo}`);
}

export function logDebug(message: string, data?: Record<string, unknown>): void {
  const logMessage = data ? `${message} ${JSON.stringify(data)}` : message;
  logger.debug(logMessage);
}

export function logWarn(message: string, data?: Record<string, unknown>): void {
  const logMessage = data ? `${message} ${JSON.stringify(data)}` : message;
  logger.warn(logMessage);
}
