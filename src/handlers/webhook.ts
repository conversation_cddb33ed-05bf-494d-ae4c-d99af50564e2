import type { BotResponse, WebhookPayload } from "../types/mod.ts";
import { logInfo, logError, logDebug } from "../utils/logger.ts";

/**
 * Handles incoming webhook requests from Mattermost
 */
export async function handleWebhook(req: Request): Promise<Response> {
  try {
    const contentType = req.headers.get("content-type");
    
    if (!contentType?.includes("application/json")) {
      return new Response(
        JSON.stringify({ error: "Content-Type must be application/json" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    const payload: WebhookPayload = await req.json();
    logDebug("Received webhook payload", { 
      channel: payload.channel_name,
      user: payload.user_name,
      text: payload.text?.substring(0, 100) + "..."
    });

    // Process the webhook payload
    const response = await processWebhookPayload(payload);

    if (response) {
      logInfo("Sending bot response", { 
        channel: payload.channel_name,
        responseType: response.response_type 
      });
      
      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    }

    // No response needed
    return new Response(null, { status: 200 });

  } catch (error) {
    logError("Error handling webhook", error as Error);
    return new Response(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    );
  }
}

/**
 * Process the webhook payload and generate a response
 */
async function processWebhookPayload(
  payload: WebhookPayload,
): Promise<BotResponse | null> {
  const { text, user_name, channel_name, trigger_word } = payload;

  // Skip messages from the bot itself (if we can identify them)
  if (user_name === "chatbot" || user_name.includes("bot")) {
    return null;
  }

  // Remove the trigger word from the message
  const message = text.replace(trigger_word, "").trim();
  
  if (!message) {
    return {
      text: "Hello! How can I help you today?",
      response_type: "ephemeral",
    };
  }

  // Basic command processing (you can expand this)
  const response = await processCommand(message, payload);
  return response;
}

/**
 * Process bot commands
 */
async function processCommand(
  message: string,
  payload: WebhookPayload,
): Promise<BotResponse> {
  const command = message.toLowerCase().split(" ")[0];

  switch (command) {
    case "help":
      return {
        text: `Available commands:
• \`help\` - Show this help message
• \`ping\` - Check if the bot is responding
• \`echo <message>\` - Echo back your message
• \`time\` - Get current server time`,
        response_type: "ephemeral",
      };

    case "ping":
      return {
        text: "Pong! 🏓 Bot is alive and responding.",
        response_type: "ephemeral",
      };

    case "echo":
      const echoMessage = message.substring(4).trim();
      return {
        text: echoMessage || "Nothing to echo!",
        response_type: "in_channel",
      };

    case "time":
      return {
        text: `Current server time: ${new Date().toISOString()}`,
        response_type: "ephemeral",
      };

    default:
      return {
        text: `I don't understand the command "${command}". Type \`help\` to see available commands.`,
        response_type: "ephemeral",
      };
  }
}
