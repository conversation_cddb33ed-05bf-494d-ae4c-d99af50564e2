#!/usr/bin/env -S deno run --allow-net --allow-read --allow-env

import { serve } from "@std/http/server";
import { config, validateConfig } from "./config/mod.ts";
import { router } from "./router.ts";
import { logInfo, logError } from "./utils/logger.ts";

/**
 * Main application entry point
 */
async function main(): Promise<void> {
  try {
    // Validate configuration
    validateConfig();
    logInfo("Configuration validated successfully");

    // Create logs directory if it doesn't exist
    try {
      await Deno.mkdir("logs", { recursive: true });
    } catch (error) {
      if (!(error instanceof Deno.errors.AlreadyExists)) {
        logError("Failed to create logs directory", error as Error);
      }
    }

    // Start the server
    logInfo(`Starting Mattermost Chatbot server on port ${config.port}`);
    logInfo(`Mattermost URL: ${config.mattermostUrl}`);
    logInfo(`Log level: ${config.logLevel}`);

    const server = serve(router, {
      port: config.port,
      onListen: ({ port, hostname }) => {
        logInfo(`Server running on http://${hostname}:${port}`);
        logInfo("Available endpoints:");
        logInfo("  GET  / - Server information");
        logInfo("  GET  /health - Health check");
        logInfo("  GET  /ready - Readiness check");
        logInfo("  POST /webhook - Mattermost webhook handler");
      },
    });

    // Handle graceful shutdown
    const shutdown = () => {
      logInfo("Shutting down server...");
      server.shutdown();
      Deno.exit(0);
    };

    // Listen for shutdown signals
    Deno.addSignalListener("SIGINT", shutdown);
    Deno.addSignalListener("SIGTERM", shutdown);

    // Wait for the server to finish
    await server.finished;

  } catch (error) {
    logError("Failed to start server", error as Error);
    Deno.exit(1);
  }
}

// Run the application
if (import.meta.main) {
  await main();
}
