#!/usr/bin/env -S deno run --allow-net --allow-read --allow-env

import { config, validateConfig } from "./config/mod.ts";
import { router } from "./router.ts";
import { logInfo, logError } from "./utils/logger.ts";

/**
 * Main application entry point
 */
async function main(): Promise<void> {
  try {
    // Validate configuration
    validateConfig();
    logInfo("Configuration validated successfully");

    // Create logs directory if it doesn't exist
    try {
      await Deno.mkdir("logs", { recursive: true });
    } catch (error) {
      if (!(error instanceof Deno.errors.AlreadyExists)) {
        logError("Failed to create logs directory", error as Error);
      }
    }

    // Start the server
    logInfo(`Starting Mattermost Chatbot server on port ${config.port}`);
    logInfo(`Mattermost URL: ${config.mattermostUrl}`);
    logInfo(`Log level: ${config.logLevel}`);

    logInfo(`Server running on http://localhost:${config.port}`);
    logInfo("Available endpoints:");
    logInfo("  GET  / - Server information");
    logInfo("  GET  /health - Health check");
    logInfo("  GET  /ready - Readiness check");
    logInfo("  POST /webhook - Mattermost webhook handler");

    // Handle graceful shutdown
    const shutdown = () => {
      logInfo("Shutting down server...");
      Deno.exit(0);
    };

    // Listen for shutdown signals
    Deno.addSignalListener("SIGINT", shutdown);
    Deno.addSignalListener("SIGTERM", shutdown);

    // Start the server using Deno.serve
    await Deno.serve({
      port: config.port,
      hostname: "0.0.0.0",
    }, router).finished;

  } catch (error) {
    logError("Failed to start server", error as Error);
    Deno.exit(1);
  }
}

// Run the application
if (import.meta.main) {
  await main();
}
