import { config } from "../config/mod.ts";
import { logWarn } from "../utils/logger.ts";

/**
 * Validates webhook secret if configured
 */
export function validateWebhookSecret(req: Request): boolean {
  if (!config.webhookSecret) {
    return true; // No secret configured, allow all requests
  }

  const token = req.headers.get("Authorization")?.replace("Bearer ", "") ||
    new URL(req.url).searchParams.get("token");

  if (!token) {
    logWarn("Missing webhook token in request");
    return false;
  }

  if (token !== config.webhookSecret) {
    logWarn("Invalid webhook token provided");
    return false;
  }

  return true;
}

/**
 * Middleware to check webhook authentication
 */
export function authMiddleware(req: Request): Response | null {
  if (!validateWebhookSecret(req)) {
    return new Response(
      JSON.stringify({ error: "Unauthorized" }),
      {
        status: 401,
        headers: { "Content-Type": "application/json" },
      },
    );
  }

  return null; // Continue to next middleware/handler
}
