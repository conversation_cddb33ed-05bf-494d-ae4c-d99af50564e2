import { corsMiddleware, addCorsHeaders } from "./middleware/cors.ts";
import { authMiddleware } from "./middleware/auth.ts";
import { handleWebhook } from "./handlers/webhook.ts";
import { handleHealth, handleReady } from "./handlers/health.ts";
import { logInfo, logError } from "./utils/logger.ts";

/**
 * Main request router
 */
export async function router(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  logInfo(`${method} ${path}`);

  try {
    // Handle CORS preflight requests
    const corsResponse = corsMiddleware(req);
    if (corsResponse) {
      return corsResponse;
    }

    // Route handling
    let response: Response;

    switch (path) {
      case "/":
        response = handleRoot(req);
        break;

      case "/health":
        response = handleHealth(req);
        break;

      case "/ready":
        response = handleReady(req);
        break;

      case "/webhook":
        if (method !== "POST") {
          response = new Response(
            JSON.stringify({ error: "Method not allowed" }),
            {
              status: 405,
              headers: { "Content-Type": "application/json" },
            },
          );
          break;
        }

        // Apply auth middleware for webhook
        const authResponse = authMiddleware(req);
        if (authResponse) {
          response = authResponse;
          break;
        }

        response = await handleWebhook(req);
        break;

      default:
        response = handleNotFound(req);
        break;
    }

    // Add CORS headers to all responses
    return addCorsHeaders(response);

  } catch (error) {
    logError("Router error", error as Error, { path, method });
    const errorResponse = new Response(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    );
    return addCorsHeaders(errorResponse);
  }
}

/**
 * Root endpoint handler
 */
function handleRoot(_req: Request): Response {
  const info = {
    name: "Mattermost Chatbot",
    version: "1.0.0",
    description: "A Deno-based chatbot for Mattermost",
    endpoints: {
      health: "/health",
      ready: "/ready",
      webhook: "/webhook",
    },
    timestamp: new Date().toISOString(),
  };

  return new Response(JSON.stringify(info, null, 2), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
}

/**
 * 404 handler
 */
function handleNotFound(_req: Request): Response {
  return new Response(
    JSON.stringify({ error: "Not found" }),
    {
      status: 404,
      headers: { "Content-Type": "application/json" },
    },
  );
}
